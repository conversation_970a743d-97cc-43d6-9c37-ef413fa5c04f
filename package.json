{"$schema": "https://www.raycast.com/schemas/extension.json", "name": "hijr-almanac", "title": "Hijr Almanac", "description": "An extension to know the date of hijr and knowing praying times easily", "icon": "extension-icon.png", "author": "satria_aji_putra", "license": "MIT", "commands": [{"name": "pray", "title": "pray", "subtitle": "Get upcoming praying time", "description": "Get upcoming praying time", "mode": "view"}], "preferences": [{"name": "latitude", "type": "textfield", "required": false, "title": "Latitude", "description": "Your location's latitude (leave empty for auto-detection)", "placeholder": "21.4225"}, {"name": "longitude", "type": "textfield", "required": false, "title": "Longitude", "description": "Your location's longitude (leave empty for auto-detection)", "placeholder": "39.8262"}, {"name": "city", "type": "textfield", "required": false, "title": "City", "description": "Your city name (optional)", "placeholder": "Mecca"}, {"name": "calculationMethod", "type": "dropdown", "required": false, "title": "Calculation Method", "description": "Method used for prayer time calculations", "default": "MuslimWorldLeague", "data": [{"title": "Muslim World League", "value": "MuslimWorldLeague"}, {"title": "Egyptian General Authority", "value": "Egyptian"}, {"title": "University of Islamic Sciences, Karachi", "value": "Karachi"}, {"title": "Umm Al-Qura University, Makkah", "value": "UmmAlQura"}, {"title": "Islamic Society of North America", "value": "NorthAmerica"}, {"title": "Moonsighting Committee Worldwide", "value": "MoonsightingCommittee"}, {"title": "Dubai", "value": "Dubai"}, {"title": "Kuwait", "value": "Kuwait"}, {"title": "Qatar", "value": "Qatar"}]}, {"name": "madhab", "type": "dropdown", "required": false, "title": "<PERSON><PERSON>", "description": "Islamic school of thought for Asr calculation", "default": "<PERSON><PERSON><PERSON>", "data": [{"title": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>", "value": "<PERSON><PERSON><PERSON>"}, {"title": "<PERSON><PERSON><PERSON>", "value": "<PERSON><PERSON><PERSON>"}]}], "dependencies": {"@raycast/api": "^1.100.3", "@raycast/utils": "^1.17.0", "adhan": "^4.4.3"}, "devDependencies": {"@raycast/eslint-config": "^2.0.4", "@types/node": "22.13.10", "@types/react": "19.0.10", "eslint": "^9.22.0", "prettier": "^3.5.3", "typescript": "^5.8.2"}, "scripts": {"build": "ray build", "dev": "ray develop", "fix-lint": "ray lint --fix", "lint": "ray lint", "prepublishOnly": "echo \"\\n\\nIt seems like you are trying to publish the Raycast extension to npm.\\n\\nIf you did intend to publish it to npm, remove the \\`prepublishOnly\\` script and rerun \\`npm publish\\` again.\\nIf you wanted to publish it to the Raycast Store instead, use \\`npm run publish\\` instead.\\n\\n\" && exit 1", "publish": "npx @raycast/api@latest publish"}}