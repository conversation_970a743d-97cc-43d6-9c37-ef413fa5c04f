#!/usr/bin/env bun

import { HijrAlmanacService } from './src/services/HijrAlmanacService';
import { PrayerTimeService } from './src/services/PrayerTimeService';

async function testInstantPrayerCommand() {
  console.log('🧪 Testing Instant Prayer Command...\n');
  
  try {
    // Test the same logic as the instant command
    const nextPrayerData = await HijrAlmanacService.getNextPrayerTime();
    const { result: nextPrayer, location } = nextPrayerData;
    
    // Format the prayer time for display
    const timeStr = PrayerTimeService.formatPrayerTime(nextPrayer.prayer.time);
    const locationStr = location.city ? `${location.city}${location.country ? `, ${location.country}` : ''}` : 'Current Location';
    
    // Create the display message
    let displayMessage = `${nextPrayer.prayer.displayName} - ${timeStr}`;
    
    if (nextPrayer.timeUntil) {
      displayMessage += ` (in ${nextPrayer.timeUntil})`;
    }
    
    if (!nextPrayer.isToday) {
      displayMessage += ' (Tomorrow)';
    }
    
    displayMessage += ` • ${locationStr}`;
    
    console.log('✅ Instant Prayer Command Test Results:');
    console.log(`   Next Prayer: ${displayMessage}`);
    console.log(`   Location: ${location.latitude}, ${location.longitude}`);
    console.log(`   Time Until: ${nextPrayer.timeUntil || 'N/A'}`);
    console.log(`   Is Today: ${nextPrayer.isToday}`);
    
    return true;
  } catch (error) {
    console.error('❌ Test failed:', error);
    return false;
  }
}

async function main() {
  console.log('🚀 Running Hijr Almanac Extension Tests\n');
  
  const success = await testInstantPrayerCommand();
  
  if (success) {
    console.log('\n🎉 All tests passed! The instant prayer command should work correctly.');
  } else {
    console.log('\n💥 Tests failed! Please check the implementation.');
    process.exit(1);
  }
}

main().catch(console.error);
