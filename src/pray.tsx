import { Detail, ActionPanel, Action, showToast, Toast } from "@raycast/api";
import { useEffect, useState } from "react";
import { HijrAlmanacService } from "./services/HijrAlmanacService";
import { PrayerTimeService } from "./services/PrayerTimeService";
import { NextPrayerResult, Location, PrayerTime } from "./types";

interface PrayerData {
  nextPrayer: NextPrayerResult;
  allPrayers: PrayerTime[];
  location: Location;
}

export default function Command() {
  const [prayerData, setPrayerData] = useState<PrayerData | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    loadPrayerTimes();
  }, []);

  const loadPrayerTimes = async () => {
    try {
      setIsLoading(true);
      setError(null);

      // Load both next prayer and all prayers concurrently
      const [nextPrayerData, todayPrayersData] = await Promise.all([
        HijrAlmanacService.getNextPrayerTime(),
        HijrAlmanacService.getTodayPrayerTimes()
      ]);

      setPrayerData({
        nextPrayer: nextPrayerData.result,
        allPrayers: todayPrayersData.prayers,
        location: nextPrayerData.location
      });
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to load prayer times';
      setError(errorMessage);
      showToast({
        style: Toast.Style.Failure,
        title: "Error",
        message: errorMessage
      });
    } finally {
      setIsLoading(false);
    }
  };

  const refreshPrayerTimes = async () => {
    showToast({
      style: Toast.Style.Animated,
      title: "Refreshing prayer times..."
    });

    HijrAlmanacService.clearLocationCache();
    await loadPrayerTimes();

    showToast({
      style: Toast.Style.Success,
      title: "Prayer times updated"
    });
  };

  if (isLoading) {
    return <Detail isLoading={true} markdown="Loading prayer times..." />;
  }

  if (error || !prayerData) {
    return (
      <Detail
        markdown={`# Error\n\n${error || 'Failed to load prayer times'}\n\nPlease check your internet connection and location permissions.`}
        actions={
          <ActionPanel>
            <Action title="Retry" onAction={loadPrayerTimes} />
          </ActionPanel>
        }
      />
    );
  }

  const nextPrayerDisplay = HijrAlmanacService.formatNextPrayerDisplay(
    prayerData.nextPrayer,
    prayerData.location
  );

  const allPrayersDisplay = HijrAlmanacService.formatAllPrayersDisplay(
    prayerData.allPrayers,
    prayerData.location
  );

  return (
    <Detail
      markdown={`# Next Prayer\n\n${nextPrayerDisplay}\n\n---\n\n${allPrayersDisplay}`}
      actions={
        <ActionPanel>
          <Action title="Refresh" onAction={refreshPrayerTimes} />
          <Action.CopyToClipboard
            title="Copy Next Prayer Time"
            content={`${prayerData.nextPrayer.prayer.displayName} - ${PrayerTimeService.formatPrayerTime(prayerData.nextPrayer.prayer.time)}`}
          />
        </ActionPanel>
      }
    />
  );
}
