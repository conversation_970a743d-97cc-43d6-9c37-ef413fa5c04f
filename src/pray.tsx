import { List, ActionPanel, Action, showToast, Toast, Icon } from "@raycast/api";
import { useEffect, useState } from "react";
import { HijrAlmanacService } from "./services/HijrAlmanacService";
import { PrayerTimeService } from "./services/PrayerTimeService";
import { NextPrayerResult, Location } from "./types";

interface PrayerData {
  nextPrayer: NextPrayerResult;
  location: Location;
}

export default function Command() {
  const [prayerData, setPrayerData] = useState<PrayerData | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    loadPrayerTime();
  }, []);

  const loadPrayerTime = async () => {
    try {
      setIsLoading(true);
      setError(null);

      const nextPrayerData = await HijrAlmanacService.getNextPrayerTime();
      const { result: nextPrayer, location } = nextPrayerData;

      setPrayerData({
        nextPrayer,
        location
      });
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to load prayer times';
      setError(errorMessage);
      showToast({
        style: Toast.Style.Failure,
        title: "Error",
        message: errorMessage
      });
    } finally {
      setIsLoading(false);
    }
  };

  if (isLoading) {
    return <List isLoading={true} searchBarPlaceholder="Loading prayer time..." />;
  }

  if (error || !prayerData) {
    return (
      <List searchBarPlaceholder="Prayer time search">
        <List.Item
          title="Error loading prayer time"
          subtitle={error || 'Failed to load prayer times'}
          icon={Icon.ExclamationMark}
          actions={
            <ActionPanel>
              <Action title="Retry" onAction={loadPrayerTime} />
            </ActionPanel>
          }
        />
      </List>
    );
  }

  const { nextPrayer, location } = prayerData;

  // Format the prayer time for display
  const timeStr = PrayerTimeService.formatPrayerTime(nextPrayer.prayer.time);
  const locationStr = location.city ? `${location.city}${location.country ? `, ${location.country}` : ''}` : 'Current Location';

  // Create the title and subtitle
  let title = `${nextPrayer.prayer.displayName} - ${timeStr}`;

  if (nextPrayer.timeUntil) {
    title += ` (in ${nextPrayer.timeUntil})`;
  }

  if (!nextPrayer.isToday) {
    title += ' (Tomorrow)';
  }

  const subtitle = `📍 ${locationStr}`;

  return (
    <List searchBarPlaceholder="Prayer time search">
      <List.Item
        title={title}
        subtitle={subtitle}
        icon="🕌"
        actions={
          <ActionPanel>
            <Action.CopyToClipboard
              title="Copy Prayer Time"
              content={`${nextPrayer.prayer.displayName} - ${timeStr}`}
            />
            <Action title="Refresh" onAction={loadPrayerTime} />
          </ActionPanel>
        }
      />
    </List>
  );
}
